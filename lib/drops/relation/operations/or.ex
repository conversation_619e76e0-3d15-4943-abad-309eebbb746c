defmodule Drops.Relation.Operations.Or do
  @moduledoc """
  An OR operation that combines two relation queries.

  This struct represents an OR operation between two queryable relations.
  It implements the Ecto.Queryable protocol to convert the OR operation
  into an Ecto query with appropriate WHERE clauses.
  """

  defstruct [:left, :right, :relation_module]

  @type t :: %__MODULE__{
          left: any(),
          right: any(),
          relation_module: module()
        }

  @doc """
  Creates a new OR operation between two queryable relations.

  ## Parameters

  - `left` - The left side of the OR operation (a queryable relation)
  - `right` - The right side of the OR operation (a queryable relation)
  - `relation_module` - The relation module for context

  ## Returns

  A new `%Drops.Relation.Operations.Or{}` struct.

  ## Examples

      left_relation = Users.get_by_name("<PERSON>")
      right_relation = Users.get_by_name("<PERSON>")
      or_operation = Drops.Relation.Operations.Or.new(left_relation, right_relation, Users)
  """
  @spec new(any(), any(), module()) :: t()
  def new(left, right, relation_module) do
    %__MODULE__{
      left: left,
      right: right,
      relation_module: relation_module
    }
  end
end

defimpl Ecto.Queryable, for: Drops.Relation.Operations.Or do
  def to_query(%Drops.Relation.Operations.Or{
        left: left,
        right: right,
        relation_module: relation_module
      }) do
    # Convert both sides to Ecto queries
    left_query = Ecto.Queryable.to_query(left)
    right_query = Ecto.Queryable.to_query(right)

    # Start with the base schema query
    base_query = Ecto.Queryable.to_query(relation_module.__schema_module__())

    # Apply left conditions first
    query_with_left = apply_where_conditions(base_query, left_query)

    # Then apply right conditions with OR
    apply_or_where_conditions(query_with_left, right_query)
  end

  # Apply WHERE conditions from a source query to the target query
  defp apply_where_conditions(target_query, source_query) do
    case source_query.wheres do
      [] ->
        target_query

      wheres ->
        Enum.reduce(wheres, target_query, fn where_expr, acc_query ->
          # Apply each WHERE condition to the target query
          %{acc_query | wheres: acc_query.wheres ++ [where_expr]}
        end)
    end
  end

  # Apply WHERE conditions from a source query to the target query using OR
  defp apply_or_where_conditions(target_query, source_query) do
    case source_query.wheres do
      [] ->
        target_query

      wheres ->
        Enum.reduce(wheres, target_query, fn where_expr, acc_query ->
          # Convert the WHERE expression to use OR operator
          or_where_expr = %{where_expr | op: :or}
          %{acc_query | wheres: acc_query.wheres ++ [or_where_expr]}
        end)
    end
  end
end

defimpl Enumerable, for: Drops.Relation.Operations.Or do
  def count(or_operation) do
    count =
      Drops.Relation.Plugins.Reading.count(
        relation: or_operation.relation_module,
        queryable: or_operation
      )

    {:ok, count}
  rescue
    _ -> {:error, __MODULE__}
  end

  def member?(or_operation, element) do
    # Convert to list and check membership
    list = Enum.to_list(or_operation)
    {:ok, Enum.member?(list, element)}
  end

  def slice(or_operation) do
    # Convert to list for slicing
    list = Enum.to_list(or_operation)
    {:ok, Enum.count(list), &Enum.slice(list, &1, &2)}
  end

  def reduce(or_operation, acc, fun) do
    # Execute the query and reduce over results
    results =
      Drops.Relation.Plugins.Reading.all(
        relation: or_operation.relation_module,
        queryable: or_operation
      )

    Enumerable.List.reduce(results, acc, fun)
  end
end
